"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Html } from "@react-three/drei"
import HeroSection from "@/components/hero-section"
import ProjectsSection from "@/components/projects-section"
import SkillsSection from "@/components/skills-section"
import EducationSection from "@/components/education-section"
import ContactSection from "@/components/contact-section"
import Header from "@/components/header"
import LoadingSpinner from "@/components/loading-spinner"
import ErrorBoundary from "@/components/error-boundary"
import Footer from "@/components/footer"
import BackToTop from "@/components/back-to-top"

export default function Portfolio() {
  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-x-hidden">
      <Header />

      {/* Hero Section with 3D Background */}
      <section id="hero" className="relative h-screen">
        <ErrorBoundary>
          <Canvas camera={{ position: [0, 0, 5], fov: 75 }} className="absolute inset-0">
            <Suspense
              fallback={
                <Html center>
                  <LoadingSpinner />
                </Html>
              }
            >
              <Environment preset="night" />
              <HeroSection />
            </Suspense>
          </Canvas>
        </ErrorBoundary>

        {/* Hero Content Overlay */}
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center px-4">
            <h1 className="text-5xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white to-teal-400 bg-clip-text text-transparent">
              Karan Ashish
            </h1>
            <h2 className="text-xl md:text-2xl text-teal-400 mb-6 font-light">Full Stack Developer</h2>
            <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Building immersive digital experiences with cutting-edge technology and modern web frameworks
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
                className="px-8 py-3 bg-teal-600 hover:bg-teal-700 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
              >
                View My Work
              </button>
              <button
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="px-8 py-3 border-2 border-teal-400 text-teal-400 hover:bg-teal-400 hover:text-slate-900 rounded-lg font-semibold transition-all duration-300"
              >
                Get In Touch
              </button>
            </div>

            {/* Scroll Indicator */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
              <div className="w-6 h-10 border-2 border-teal-400 rounded-full flex justify-center">
                <div className="w-1 h-3 bg-teal-400 rounded-full mt-2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="relative min-h-screen py-20">
        <ProjectsSection />
      </section>

      {/* Skills Section */}
      <section id="skills" className="relative min-h-screen py-20">
        <SkillsSection />
      </section>

      {/* Education Section */}
      <section id="education" className="relative min-h-screen py-20">
        <EducationSection />
      </section>

      {/* Contact Section */}
      <section id="contact" className="relative min-h-screen py-20">
        <ContactSection />
      </section>

      {/* Footer */}
      <Footer />

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  )
}
