"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { GraduationCap, Briefcase, MapPin, Calendar, BookOpen, Lightbulb } from "lucide-react"

export default function EducationSection() {
  const [flippedCard, setFlippedCard] = useState<string | null>(null)

  const handleCardFlip = (cardId: string) => {
    setFlippedCard(flippedCard === cardId ? null : cardId)
  }

  return (
    <div className="container mx-auto px-6">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-teal-400 bg-clip-text text-transparent">
          Education & Experience
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">Academic background and professional journey</p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        {/* Education Card */}
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300 group">
          <CardHeader>
            <div className="flex items-center mb-2">
              <GraduationCap className="w-6 h-6 text-teal-400 mr-3" />
              <Badge variant="secondary" className="bg-teal-600 text-white">
                Education
              </Badge>
            </div>
            <CardTitle className="text-white group-hover:text-teal-400 transition-colors">
              Delhi Technological University
            </CardTitle>
            <CardDescription className="text-gray-300">
              Bachelor of Technology - Electronics & Communication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <Calendar className="w-4 h-4 mr-2 text-teal-400" />
                <span>August 2017 - 2022</span>
              </div>
              <div className="flex items-start text-gray-300">
                <MapPin className="w-4 h-4 mr-2 text-teal-400 mt-1 flex-shrink-0" />
                <span className="text-sm">Bawana Rd, Shahbad Daulatpur Village, Rohini, Delhi, 110042</span>
              </div>
              <p className="text-gray-300 text-sm mt-4">
                Comprehensive engineering education focusing on electronics, communication systems, and emerging
                technologies. Developed strong analytical and problem-solving skills through hands-on projects and
                theoretical coursework.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Interests Card */}
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300 group">
          <CardHeader>
            <div className="flex items-center mb-2">
              <BookOpen className="w-6 h-6 text-purple-400 mr-3" />
              <Badge variant="secondary" className="bg-purple-600 text-white">
                Interests
              </Badge>
            </div>
            <CardTitle className="text-white group-hover:text-purple-400 transition-colors">
              Personal Interests
            </CardTitle>
            <CardDescription className="text-gray-300">Beyond Technology</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center text-gray-300">
                <BookOpen className="w-5 h-5 mr-3 text-purple-400" />
                <div>
                  <h4 className="font-semibold text-white">Avid Reader</h4>
                  <p className="text-sm text-gray-400">Passionate about continuous learning through literature</p>
                </div>
              </div>
              <p className="text-gray-300 text-sm">
                Reading helps me stay curious and informed about various topics, from technology trends to personal
                development, enhancing both my professional and personal growth.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Experience Cards */}
      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        {/* Jingle Infosolutions Internship */}
        <Card
          className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm cursor-pointer transition-all duration-500 transform-gpu ${
            flippedCard === "jingle" ? "rotate-y-180" : ""
          }`}
          onClick={() => handleCardFlip("jingle")}
          style={{ transformStyle: "preserve-3d" }}
        >
          <div className={`${flippedCard === "jingle" ? "opacity-0" : "opacity-100"} transition-opacity duration-300`}>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Briefcase className="w-6 h-6 text-blue-400 mr-3" />
                <Badge variant="secondary" className="bg-blue-600 text-white">
                  Internship
                </Badge>
              </div>
              <CardTitle className="text-white">Jingle Infosolutions Pvt Ltd</CardTitle>
              <CardDescription className="text-gray-300">Digital Marketing Intern</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm mb-4">
                Focused on content creation and digital marketing strategies for various tech products.
              </p>
              <p className="text-teal-400 text-sm">Click to see detailed responsibilities...</p>
            </CardContent>
          </div>

          {flippedCard === "jingle" && (
            <div
              className="absolute inset-0 p-6 bg-slate-800/90 rounded-lg rotate-y-180"
              style={{ backfaceVisibility: "hidden" }}
            >
              <div className="h-full flex flex-col justify-center">
                <h3 className="text-xl font-bold text-white mb-4">Key Responsibilities</h3>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li>• Wrote comprehensive articles on different tech products</li>
                  <li>• Developed content marketing strategies</li>
                  <li>• Researched product features and benefits</li>
                  <li>• Created engaging content for digital platforms</li>
                  <li>• Collaborated with marketing team on campaigns</li>
                </ul>
                <p className="text-blue-400 text-sm mt-4 font-semibold">
                  Enhanced technical writing and marketing communication skills
                </p>
                <p className="text-teal-400 text-xs mt-2">Click again to flip back</p>
              </div>
            </div>
          )}
        </Card>

        {/* TEN Internship */}
        <Card
          className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm cursor-pointer transition-all duration-500 transform-gpu ${
            flippedCard === "ten" ? "rotate-y-180" : ""
          }`}
          onClick={() => handleCardFlip("ten")}
          style={{ transformStyle: "preserve-3d" }}
        >
          <div className={`${flippedCard === "ten" ? "opacity-0" : "opacity-100"} transition-opacity duration-300`}>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Briefcase className="w-6 h-6 text-green-400 mr-3" />
                <Badge variant="secondary" className="bg-green-600 text-white">
                  Internship
                </Badge>
              </div>
              <CardTitle className="text-white">The Entrepreneurship Network</CardTitle>
              <CardDescription className="text-gray-300">Product Management Associate Intern</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center text-gray-300 mb-4">
                <Calendar className="w-4 h-4 mr-2 text-green-400" />
                <span className="text-sm">July 2021 - September 2021</span>
              </div>
              <p className="text-gray-300 text-sm mb-4">
                Contributed to product improvement and pricing strategies through market analysis.
              </p>
              <p className="text-teal-400 text-sm">Click to see detailed experience...</p>
            </CardContent>
          </div>

          {flippedCard === "ten" && (
            <div
              className="absolute inset-0 p-6 bg-slate-800/90 rounded-lg rotate-y-180"
              style={{ backfaceVisibility: "hidden" }}
            >
              <div className="h-full flex flex-col justify-center">
                <h3 className="text-xl font-bold text-white mb-4">Key Achievements</h3>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li>• Improved product pricing through competitive analysis</li>
                  <li>• Conducted comprehensive market research</li>
                  <li>• Learned Agile methodology and implementation</li>
                  <li>• Created product mockups and prototypes</li>
                  <li>• Studied product lifecycle management</li>
                  <li>• Analyzed case studies of different companies</li>
                </ul>
                <p className="text-green-400 text-sm mt-4 font-semibold">
                  Gained valuable product management and business analysis skills
                </p>
                <p className="text-teal-400 text-xs mt-2">Click again to flip back</p>
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Academic Projects Section */}
      <div className="mt-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
            <Lightbulb className="w-8 h-8 text-yellow-400 mr-3" />
            Academic Projects
          </h3>
          <p className="text-gray-300">Hands-on engineering projects during B.Tech</p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {/* Project 1 */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300 group">
            <CardHeader>
              <Badge variant="secondary" className="bg-red-600 text-white w-fit mb-2">
                2nd Year
              </Badge>
              <CardTitle className="text-white group-hover:text-red-400 transition-colors">
                Home Security System
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm mb-4">
                Developed a comprehensive security solution using IR sensors strategically placed on doors and windows,
                connected to an integrated alarm system for real-time intrusion detection.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 bg-red-600/20 text-red-400 rounded text-xs">IR Sensors</span>
                <span className="px-2 py-1 bg-red-600/20 text-red-400 rounded text-xs">Alarm System</span>
                <span className="px-2 py-1 bg-red-600/20 text-red-400 rounded text-xs">Electronics</span>
              </div>
            </CardContent>
          </Card>

          {/* Project 2 */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300 group">
            <CardHeader>
              <Badge variant="secondary" className="bg-blue-600 text-white w-fit mb-2">
                3rd Year
              </Badge>
              <CardTitle className="text-white group-hover:text-blue-400 transition-colors">
                GPS Vehicle Tracking System
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm mb-4">
                Created a real-time vehicle tracking solution by integrating a GSM SIM card with Arduino microcontroller
                to develop GPS tracking software for location monitoring.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 bg-blue-600/20 text-blue-400 rounded text-xs">Arduino</span>
                <span className="px-2 py-1 bg-blue-600/20 text-blue-400 rounded text-xs">GSM</span>
                <span className="px-2 py-1 bg-blue-600/20 text-blue-400 rounded text-xs">GPS</span>
              </div>
            </CardContent>
          </Card>

          {/* Project 3 */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300 group">
            <CardHeader>
              <Badge variant="secondary" className="bg-green-600 text-white w-fit mb-2">
                4th Year
              </Badge>
              <CardTitle className="text-white group-hover:text-green-400 transition-colors">
                IoT Garbage Collection System
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm mb-4">
                Engineered an IoT-enabled smart waste management system using Arduino, IR sensors, humidity sensors, and
                GSM connectivity to optimize garbage collection routes and schedules.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 bg-green-600/20 text-green-400 rounded text-xs">IoT</span>
                <span className="px-2 py-1 bg-green-600/20 text-green-400 rounded text-xs">Arduino</span>
                <span className="px-2 py-1 bg-green-600/20 text-green-400 rounded text-xs">Sensors</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
