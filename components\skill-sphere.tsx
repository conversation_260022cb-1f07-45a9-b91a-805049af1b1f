"use client"

import { useRef, useState, memo } from "react"
import { useFrame } from "@react-three/fiber"
import { Text, Html } from "@react-three/drei"
import type * as THREE from "three"

const skills = [
  { name: "React", position: [2, 1, 0], color: "#61DAFB" },
  { name: "Node.js", position: [-2, 1, 1], color: "#339933" },
  { name: "MongoDB", position: [1, -2, 1], color: "#47A248" },
  { name: "Express", position: [-1, -1, -2], color: "#000000" },
  { name: "TypeScript", position: [2, 0, -1], color: "#3178C6" },
  { name: "Next.js", position: [-2, 0, 0], color: "#000000" },
  { name: "Tail<PERSON>", position: [0, 2, 1], color: "#06B6D4" },
  { name: "UI/UX", position: [1, 1, 2], color: "#FF6B6B" },
  { name: "Agile", position: [-1, 2, -1], color: "#FFA500" },
  { name: "Git", position: [0, -2, -1], color: "#F05032" },
]

const SkillSphere = memo(function SkillSphere() {
  const groupRef = useRef<THREE.Group>(null!)
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null)

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.1
      groupRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.05) * 0.1
    }
  })

  return (
    <group ref={groupRef}>
      {/* Central sphere */}
      <mesh>
        <sphereGeometry args={[0.5, 32, 32]} />
        <meshStandardMaterial color="#64ffda" transparent opacity={0.3} wireframe />
      </mesh>

      {/* Skill nodes */}
      {skills.map((skill, index) => (
        <group key={skill.name} position={skill.position}>
          <mesh
            onPointerEnter={() => setHoveredSkill(skill.name)}
            onPointerLeave={() => setHoveredSkill(null)}
            scale={hoveredSkill === skill.name ? 1.2 : 1}
          >
            <sphereGeometry args={[0.15, 16, 16]} />
            <meshStandardMaterial color={skill.color} />
          </mesh>

          <Text
            position={[0, -0.4, 0]}
            fontSize={0.2}
            color="white"
            anchorX="center"
            anchorY="middle"
          >
            {skill.name}
          </Text>

          {/* Connection lines to center */}
          <mesh position={[0, 0, 0]} lookAt={[0, 0, 0]}>
            <cylinderGeometry
              args={[
                0.01,
                0.01,
                Math.sqrt(skill.position[0] ** 2 + skill.position[1] ** 2 + skill.position[2] ** 2),
                8,
              ]}
            />
            <meshBasicMaterial color="#64ffda" transparent opacity={0.3} />
          </mesh>

          {hoveredSkill === skill.name && (
            <Html position={[0, 0.8, 0]} center>
              <div className="bg-slate-800 text-white p-2 rounded border border-teal-400 text-sm whitespace-nowrap">
                {skill.name} Technology
              </div>
            </Html>
          )}
        </group>
      ))}

      {/* Orbital rings */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <torusGeometry args={[3, 0.02, 8, 64]} />
        <meshBasicMaterial color="#64ffda" transparent opacity={0.2} />
      </mesh>

      <mesh rotation={[0, Math.PI / 2, Math.PI / 4]}>
        <torusGeometry args={[2.5, 0.02, 8, 64]} />
        <meshBasicMaterial color="#64ffda" transparent opacity={0.1} />
      </mesh>
    </group>
  )
})

export default SkillSphere
