"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>edin, Twitter, Mail, Heart } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-slate-900/95 border-t border-slate-800 py-12">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="text-2xl font-bold bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
              <PERSON><PERSON>
            </div>
            <p className="text-gray-300 text-sm">
              Full Stack Developer passionate about creating innovative digital solutions with modern technologies.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-white font-semibold">Quick Links</h3>
            <div className="flex flex-col space-y-2">
              <button
                onClick={() => document.getElementById('hero')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-gray-300 hover:text-teal-400 transition-colors text-left text-sm"
              >
                Home
              </button>
              <button
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-gray-300 hover:text-teal-400 transition-colors text-left text-sm"
              >
                Projects
              </button>
              <button
                onClick={() => document.getElementById('skills')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-gray-300 hover:text-teal-400 transition-colors text-left text-sm"
              >
                Skills
              </button>
              <button
                onClick={() => document.getElementById('education')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-gray-300 hover:text-teal-400 transition-colors text-left text-sm"
              >
                Experience
              </button>
              <button
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-gray-300 hover:text-teal-400 transition-colors text-left text-sm"
              >
                Contact
              </button>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-white font-semibold">Get In Touch</h3>
            <div className="space-y-2">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center text-gray-300 hover:text-teal-400 transition-colors text-sm"
              >
                <Mail className="w-4 h-4 mr-2" />
                <EMAIL>
              </a>
              <p className="text-gray-300 text-sm">Dwarka Sector-8, Delhi, India</p>
            </div>
            
            {/* Social Links */}
            <div className="flex gap-3 pt-2">
              <Button
                variant="outline"
                size="icon"
                className="border-slate-600 hover:border-teal-400 hover:text-teal-400 w-8 h-8"
                onClick={() => window.open('https://github.com/Savrano123', '_blank')}
              >
                <Github className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="border-slate-600 hover:border-teal-400 hover:text-teal-400 w-8 h-8"
                onClick={() => window.open('https://linkedin.com/in/karan-ashish', '_blank')}
              >
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="border-slate-600 hover:border-teal-400 hover:text-teal-400 w-8 h-8"
                onClick={() => window.open('https://twitter.com/karanashish', '_blank')}
              >
                <Twitter className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm flex items-center">
            © {currentYear} Karan Ashish. Made with <Heart className="w-4 h-4 mx-1 text-red-400" /> and lots of coffee.
          </p>
          <p className="text-gray-400 text-sm mt-2 md:mt-0">
            Built with Next.js, React Three Fiber & Tailwind CSS
          </p>
        </div>
      </div>
    </footer>
  )
}
