"use client"

import { useState, useEffect } from "react"
import { Download, Menu, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    element?.scrollIntoView({ behavior: "smooth" })
    setIsMobileMenuOpen(false)
  }

  return (
    <header
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled ? "bg-slate-900/90 backdrop-blur-md border-b border-slate-800" : "bg-transparent"
      }`}
    >
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
            KA
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button onClick={() => scrollToSection("hero")} className="hover:text-teal-400 transition-colors">
              Home
            </button>
            <button onClick={() => scrollToSection("projects")} className="hover:text-teal-400 transition-colors">
              Projects
            </button>
            <button onClick={() => scrollToSection("skills")} className="hover:text-teal-400 transition-colors">
              Skills
            </button>
            <button onClick={() => scrollToSection("education")} className="hover:text-teal-400 transition-colors">
              Experience
            </button>
            <button onClick={() => scrollToSection("contact")} className="hover:text-teal-400 transition-colors">
              Contact
            </button>

            <Button
              className="bg-teal-600 hover:bg-teal-700 text-white"
              onClick={() => {
                // Placeholder for resume download - you can replace with actual resume file
                alert('Resume download functionality - please add your resume file to the public folder')
              }}
            >
              <Download className="w-4 h-4 mr-2" />
              Resume
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 py-4 border-t border-slate-800">
            <div className="flex flex-col space-y-4">
              <button
                onClick={() => scrollToSection("hero")}
                className="text-left hover:text-teal-400 transition-colors"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection("projects")}
                className="text-left hover:text-teal-400 transition-colors"
              >
                Projects
              </button>
              <button
                onClick={() => scrollToSection("skills")}
                className="text-left hover:text-teal-400 transition-colors"
              >
                Skills
              </button>
              <button
                onClick={() => scrollToSection("education")}
                className="text-left hover:text-teal-400 transition-colors"
              >
                Experience
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="text-left hover:text-teal-400 transition-colors"
              >
                Contact
              </button>
              <Button
                className="bg-teal-600 hover:bg-teal-700 text-white w-fit"
                onClick={() => {
                  // Placeholder for resume download - you can replace with actual resume file
                  alert('Resume download functionality - please add your resume file to the public folder')
                }}
              >
                <Download className="w-4 h-4 mr-2" />
                Resume
              </Button>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
