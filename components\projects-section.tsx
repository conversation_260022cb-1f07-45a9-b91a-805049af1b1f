"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Html } from "@react-three/drei"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink, Github } from "lucide-react"
import LoadingSpinner from "@/components/loading-spinner"
import RotatingPetAccessory from "@/components/rotating-pet-accessory"

export default function ProjectsSection() {
  return (
    <div className="container mx-auto px-6">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-teal-400 bg-clip-text text-transparent">
          Featured Projects
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Showcasing innovative solutions built with modern technologies
        </p>
      </div>

      {/* Featured Project */}
      <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
        <div className="order-2 lg:order-1">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl text-white">WoofnWhiskers</CardTitle>
                <Badge variant="secondary" className="bg-teal-600 text-white">
                  Featured
                </Badge>
              </div>
              <CardDescription className="text-gray-300">E-commerce Platform for Pet Accessories</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-6">
                A comprehensive e-commerce solution for pet accessories featuring secure payment processing, inventory
                management, and an intuitive user experience. Built with modern web technologies for optimal performance
                and scalability.
              </p>

              <div className="flex flex-wrap gap-2 mb-6">
                <Badge variant="outline" className="border-teal-400 text-teal-400">
                  React
                </Badge>
                <Badge variant="outline" className="border-teal-400 text-teal-400">
                  Node.js
                </Badge>
                <Badge variant="outline" className="border-teal-400 text-teal-400">
                  MongoDB
                </Badge>
                <Badge variant="outline" className="border-teal-400 text-teal-400">
                  Stripe API
                </Badge>
                <Badge variant="outline" className="border-teal-400 text-teal-400">
                  Express
                </Badge>
              </div>

              <div className="flex gap-4">
                <Button
                  variant="outline"
                  className="border-teal-400 text-teal-400 hover:bg-teal-400 hover:text-slate-900"
                  onClick={() => window.open('https://github.com/Savrano123/Woofnwhiskers', '_blank')}
                >
                  <Github className="w-4 h-4 mr-2" />
                  View Code
                </Button>
                <Button
                  className="bg-teal-600 hover:bg-teal-700"
                  onClick={() => window.open('https://woofnwhiskers-demo.vercel.app', '_blank')}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Live Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="order-1 lg:order-2 h-96">
          <Canvas camera={{ position: [0, 0, 5] }}>
            <Suspense
              fallback={
                <Html center>
                  <LoadingSpinner />
                </Html>
              }
            >
              <RotatingPetAccessory />
              <OrbitControls enableZoom={false} />
              <ambientLight intensity={0.5} />
              <pointLight position={[10, 10, 10]} />
            </Suspense>
          </Canvas>
        </div>
      </div>

      {/* Other Projects Grid */}
      <div className="text-center mb-12">
        <h3 className="text-3xl font-bold text-white mb-4">Other Projects</h3>
        <p className="text-gray-300">More exciting projects coming soon...</p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card
            key={i}
            className="bg-slate-800/30 border-slate-700 backdrop-blur-sm hover:bg-slate-800/50 transition-all duration-300 group"
          >
            <CardHeader>
              <CardTitle className="text-white group-hover:text-teal-400 transition-colors">Project {i + 2}</CardTitle>
              <CardDescription className="text-gray-400">Coming Soon</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg flex items-center justify-center mb-4">
                <span className="text-gray-500">Preview</span>
              </div>
              <p className="text-gray-300 text-sm">Exciting new project in development. Stay tuned for updates!</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
