"use client"

import { useRef, useMemo } from "react"
import { use<PERSON>rame, useThree } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import type * as THREE from "three"

function FloatingParticles() {
  const ref = useRef<THREE.Points>(null!)
  const { mouse } = useThree()

  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(2000 * 3)

    for (let i = 0; i < 2000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }

    return positions
  }, [])

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = state.clock.elapsedTime * 0.05
      ref.current.rotation.y = state.clock.elapsedTime * 0.075

      // React to mouse movement
      ref.current.rotation.x += mouse.y * 0.05
      ref.current.rotation.y += mouse.x * 0.05
    }
  })

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>
        <PointMaterial transparent color="#64ffda" size={0.05} sizeAttenuation={true} depthWrite={false} />
      </Points>
    </group>
  )
}

function CircuitBoard() {
  const ref = useRef<THREE.Group>(null!)

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.z = state.clock.elapsedTime * 0.02
    }
  })

  return (
    <group ref={ref}>
      {/* Circuit lines */}
      {Array.from({ length: 20 }).map((_, i) => (
        <mesh key={i} position={[(Math.random() - 0.5) * 15, (Math.random() - 0.5) * 15, -5]}>
          <boxGeometry args={[Math.random() * 2 + 0.5, 0.02, 0.02]} />
          <meshBasicMaterial color="#64ffda" transparent opacity={0.3} />
        </mesh>
      ))}
    </group>
  )
}

export default function HeroSection() {
  return (
    <>
      <FloatingParticles />
      <CircuitBoard />
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
    </>
  )
}
