"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { Html } from "@react-three/drei"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Mail, MapPin, Github, Linkedin, Twitter } from "lucide-react"
import LoadingSpinner from "@/components/loading-spinner"
import AnimatedLocationPin from "@/components/animated-location-pin"
import ErrorBoundary from "@/components/error-boundary"

export default function ContactSection() {
  return (
    <div className="container mx-auto px-6">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-teal-400 bg-clip-text text-transparent">
          Get In Touch
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">Let's collaborate on your next project</p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12 items-center">
        <div className="h-96">
          <ErrorBoundary>
            <Canvas camera={{ position: [0, 0, 5] }}>
              <Suspense
                fallback={
                  <Html center>
                    <LoadingSpinner />
                  </Html>
                }
              >
                <AnimatedLocationPin />
                <ambientLight intensity={0.6} />
                <pointLight position={[10, 10, 10]} />
              </Suspense>
            </Canvas>
          </ErrorBoundary>
        </div>

        <div className="space-y-6">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mail className="w-6 h-6 text-teal-400 mr-3" />
                Email
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4"><EMAIL></p>
              <Button
                className="bg-teal-600 hover:bg-teal-700 w-full"
                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              >
                Send Email
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <MapPin className="w-6 h-6 text-teal-400 mr-3" />
                Location
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">Dwarka Sector-8, Delhi, India</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Connect With Me</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  size="icon"
                  className="border-slate-600 hover:border-teal-400 hover:text-teal-400"
                  onClick={() => window.open('https://github.com/Savrano123', '_blank')}
                >
                  <Github className="w-5 h-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="border-slate-600 hover:border-teal-400 hover:text-teal-400"
                  onClick={() => window.open('https://linkedin.com/in/karan-ashish', '_blank')}
                >
                  <Linkedin className="w-5 h-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="border-slate-600 hover:border-teal-400 hover:text-teal-400"
                  onClick={() => window.open('https://twitter.com/karanashish', '_blank')}
                >
                  <Twitter className="w-5 h-5" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
