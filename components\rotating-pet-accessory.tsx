"use client"

import { useRef, useState } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Html } from "@react-three/drei"
import type * as THREE from "three"

export default function RotatingPetAccessory() {
  const groupRef = useRef<THREE.Group>(null!)
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.5
      groupRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1
    }
  })

  return (
    <group ref={groupRef}>
      {/* Pet Collar */}
      <mesh onPointerEnter={() => setHovered(true)} onPointerLeave={() => setHovered(false)} scale={hovered ? 1.1 : 1}>
        <torusGeometry args={[1, 0.1, 8, 32]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* Collar <PERSON>le */}
      <mesh position={[1.1, 0, 0]}>
        <boxGeometry args={[0.2, 0.3, 0.05]} />
        <meshStandardMaterial color="#FFD700" />
      </mesh>

      {/* Pet Tag */}
      <mesh position={[0, -1.3, 0]}>
        <cylinderGeometry args={[0.15, 0.15, 0.05, 8]} />
        <meshStandardMaterial color="#C0C0C0" />
      </mesh>

      {/* Floating particles around the collar */}
      {Array.from({ length: 8 }).map((_, i) => (
        <mesh
          key={i}
          position={[
            Math.cos((i / 8) * Math.PI * 2) * 2,
            Math.sin((i / 8) * Math.PI * 2) * 0.5,
            Math.sin((i / 8) * Math.PI * 2) * 2,
          ]}
        >
          <sphereGeometry args={[0.02]} />
          <meshBasicMaterial color="#64ffda" />
        </mesh>
      ))}

      {/* Hover tooltip */}
      {hovered && (
        <Html position={[0, 2, 0]} center>
          <div className="bg-slate-800 text-white p-3 rounded-lg border border-teal-400 shadow-lg">
            <h4 className="font-bold text-teal-400">Premium Pet Collar</h4>
            <p className="text-sm">Durable leather with custom engraving</p>
          </div>
        </Html>
      )}
    </group>
  )
}
