"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Html } from "@react-three/drei"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import LoadingSpinner from "@/components/loading-spinner"
import SkillSphere from "@/components/skill-sphere"
import ErrorBoundary from "@/components/error-boundary"

export default function SkillsSection() {
  return (
    <div className="container mx-auto px-6">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-teal-400 bg-clip-text text-transparent">
          Skills & Technologies
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">Interactive visualization of my technical expertise</p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12 items-center">
        <div className="h-96">
          <ErrorBoundary>
            <Canvas camera={{ position: [0, 0, 8] }}>
              <Suspense
                fallback={
                  <Html center>
                    <LoadingSpinner />
                  </Html>
                }
              >
                <SkillSphere />
                <OrbitControls enableZoom={false} autoRotate autoRotateSpeed={0.5} />
                <ambientLight intensity={0.6} />
                <pointLight position={[10, 10, 10]} />
              </Suspense>
            </Canvas>
          </ErrorBoundary>
        </div>

        <div className="space-y-6">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <span className="w-3 h-3 bg-teal-400 rounded-full mr-3"></span>
                Frontend Development
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">
                Creating responsive and interactive user interfaces with modern frameworks
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-blue-600/20 text-blue-400 rounded-full text-sm">React</span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-400 rounded-full text-sm">Next.js</span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-400 rounded-full text-sm">TypeScript</span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-400 rounded-full text-sm">Tailwind CSS</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <span className="w-3 h-3 bg-green-400 rounded-full mr-3"></span>
                Backend Development
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">Building scalable server-side applications and APIs</p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-green-600/20 text-green-400 rounded-full text-sm">Node.js</span>
                <span className="px-3 py-1 bg-green-600/20 text-green-400 rounded-full text-sm">Express</span>
                <span className="px-3 py-1 bg-green-600/20 text-green-400 rounded-full text-sm">MongoDB</span>
                <span className="px-3 py-1 bg-green-600/20 text-green-400 rounded-full text-sm">REST APIs</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <span className="w-3 h-3 bg-purple-400 rounded-full mr-3"></span>
                AI-Enhanced Development
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">
                Enhanced with AI-assisted development workflows for improved productivity
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-purple-600/20 text-purple-400 rounded-full text-sm">GitHub Copilot</span>
                <span className="px-3 py-1 bg-purple-600/20 text-purple-400 rounded-full text-sm">AI Code Review</span>
                <span className="px-3 py-1 bg-purple-600/20 text-purple-400 rounded-full text-sm">
                  Automated Testing
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
