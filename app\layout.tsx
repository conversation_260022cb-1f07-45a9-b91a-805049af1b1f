import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: '<PERSON><PERSON> - Full Stack Developer Portfolio',
  description: 'Full Stack Developer specializing in React, Node.js, and modern web technologies. Explore my projects, skills, and experience in building innovative digital solutions.',
  keywords: ['Full Stack Developer', 'React', 'Node.js', 'JavaScript', 'TypeScript', 'Web Development', 'Portfolio', 'Kara<PERSON>'],
  authors: [{ name: '<PERSON><PERSON>' }],
  creator: '<PERSON><PERSON>',
  publisher: '<PERSON><PERSON>',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://karanashish.dev',
    title: '<PERSON><PERSON> - Full Stack Developer Portfolio',
    description: 'Full Stack Developer specializing in React, Node.js, and modern web technologies. Explore my projects, skills, and experience.',
    siteName: 'Karan Ashish Portfolio',
  },
  twitter: {
    card: 'summary_large_image',
    title: '<PERSON><PERSON> - Full Stack Developer Portfolio',
    description: 'Full Stack Developer specializing in React, Node.js, and modern web technologies.',
    creator: '@karanashish',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className="antialiased">{children}</body>
    </html>
  )
}
