"use client"

import { useRef } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Text } from "@react-three/drei"
import type * as THREE from "three"

export default function AnimatedLocationPin() {
  const pinRef = useRef<THREE.Group>(null!)
  const ringsRef = useRef<THREE.Group>(null!)

  useFrame((state) => {
    if (pinRef.current) {
      pinRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.2
    }
    if (ringsRef.current) {
      ringsRef.current.rotation.z = state.clock.elapsedTime * 0.5
    }
  })

  return (
    <group>
      {/* Location Pin */}
      <group ref={pinRef}>
        {/* Pin body */}
        <mesh position={[0, 0.5, 0]}>
          <sphereGeometry args={[0.3, 16, 16]} />
          <meshStandardMaterial color="#ef4444" />
        </mesh>

        {/* Pin point */}
        <mesh position={[0, 0, 0]}>
          <coneGeometry args={[0.15, 0.5, 8]} />
          <meshStandardMaterial color="#dc2626" />
        </mesh>

        {/* Inner dot */}
        <mesh position={[0, 0.5, 0]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="white" />
        </mesh>
      </group>

      {/* Animated rings */}
      <group ref={ringsRef}>
        <mesh>
          <torusGeometry args={[1, 0.02, 8, 32]} />
          <meshBasicMaterial color="#64ffda" transparent opacity={0.6} />
        </mesh>
        <mesh>
          <torusGeometry args={[1.5, 0.02, 8, 32]} />
          <meshBasicMaterial color="#64ffda" transparent opacity={0.3} />
        </mesh>
        <mesh>
          <torusGeometry args={[2, 0.02, 8, 32]} />
          <meshBasicMaterial color="#64ffda" transparent opacity={0.1} />
        </mesh>
      </group>

      {/* Location text */}
      <Text
        position={[0, -1.5, 0]}
        fontSize={0.3}
        color="#64ffda"
        anchorX="center"
        anchorY="middle"
      >
        Delhi, India
      </Text>

      {/* Floating particles */}
      {Array.from({ length: 12 }).map((_, i) => (
        <mesh
          key={i}
          position={[
            Math.cos((i / 12) * Math.PI * 2) * 3,
            Math.sin((i / 12) * Math.PI * 2) * 0.5,
            Math.sin((i / 12) * Math.PI * 2) * 1,
          ]}
        >
          <sphereGeometry args={[0.03]} />
          <meshBasicMaterial color="#64ffda" />
        </mesh>
      ))}
    </group>
  )
}
