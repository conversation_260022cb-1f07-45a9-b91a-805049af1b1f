# Karan <PERSON>

**Full Stack Developer**

Dwarka Sector-8, New Delhi - 110077
Email: [<EMAIL>](mailto:<EMAIL>)

---

## **PROFESSIONAL EXPERIENCE**

### **<PERSON><PERSON><PERSON><PERSON> MARKETING INTERN**
**Jingle Infosolutions Pvt Ltd**
- Wrote comprehensive articles on different tech products
- Developed content marketing strategies for various technology solutions
- Enhanced technical writing and marketing communication skills

### **PRODUCT MANAGEMENT ASSOCIATE INTERN**
**The Entrepreneurship Network** | July 2021 - September 2021
- Improved product pricing through competitive and market analysis
- Conducted comprehensive market research and analysis
- Learned Agile methodology and implementation
- Created product mockups and prototypes
- Studied product lifecycle management
- Analyzed case studies of different companies and products

---

## **EDUCATION**

### **Delhi Technological University**
**Bachelor of Technology - Electronics & Communication** | August 2017 - 2022
*Bawana Rd, Shahbad Daulatpur Village, Rohini, Delhi, 110042*

### **Hope Hall Foundation School**
**Higher Secondary Education** | 2014 - 2016
*Sec-7, R.K.Puram, New Delhi 110022*

### **Kerala Education Society School**
**Matriculation** | 2003 - 2014
*Sec-8, R.K.Puram, New Delhi 110022*

---

## **TECHNICAL PROJECTS**

### **IoT Enabled Garbage Collection System** (4th Year B.Tech)
- Engineered an IoT-enabled smart waste management system
- Integrated Arduino with IR sensors, humidity sensors, and GSM connectivity
- Optimized garbage collection routes and schedules through real-time monitoring

### **GPS Enabled Vehicle Tracking System** (3rd Year B.Tech)
- Developed real-time vehicle tracking solution
- Integrated GSM SIM card with Arduino microcontroller
- Created GPS tracking software for location monitoring

### **Home Security System** (2nd Year B.Tech)
- Designed comprehensive security solution using IR sensors
- Strategically placed sensors on doors and windows
- Connected to integrated alarm system for real-time intrusion detection

---

## **TECHNICAL SKILLS**

- **Frontend Development:** React, Next.js, TypeScript, Tailwind CSS
- **Backend Development:** Node.js, Express, MongoDB, REST APIs
- **Programming Languages:** JavaScript, TypeScript, Python
- **Tools & Technologies:** Git, GitHub, Arduino, IoT Systems
- **Development Practices:** Agile methodology, Product lifecycle management

---

## **INTERESTS**

**Avid Reader** - Passionate about continuous learning through literature, staying curious and informed about technology trends and personal development

