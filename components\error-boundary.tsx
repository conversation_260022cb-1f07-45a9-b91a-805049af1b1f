"use client"

import React from 'react'
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('3D Component Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="flex flex-col items-center justify-center p-8 bg-slate-800/50 rounded-lg border border-slate-700">
          <AlertTriangle className="w-12 h-12 text-yellow-400 mb-4" />
          <h3 className="text-white text-lg font-semibold mb-2">3D Component Error</h3>
          <p className="text-gray-300 text-sm text-center mb-4">
            Sorry, there was an issue loading the 3D visualization. This might be due to browser compatibility or WebGL support.
          </p>
          <Button
            onClick={() => this.setState({ hasError: false })}
            variant="outline"
            className="border-teal-400 text-teal-400 hover:bg-teal-400 hover:text-slate-900"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
